{"summary": {"total_tests": 34, "passed_tests": 34, "failed_tests": 0, "success_rate": 100.0}, "detailed_results": [{"test": "Test Data Setup", "status": "PASS", "message": "All test data created successfully", "timestamp": "2025-07-31T11:28:06.122118"}, {"test": "Login Test - admin", "status": "PASS", "message": "User +123456701d logged in successfully", "timestamp": "2025-07-31T11:28:06.191501"}, {"test": "<PERSON><PERSON> Test - doctor", "status": "PASS", "message": "User +123456801d logged in successfully", "timestamp": "2025-07-31T11:28:06.242056"}, {"test": "<PERSON><PERSON> Test - nurse", "status": "PASS", "message": "User +123456901d logged in successfully", "timestamp": "2025-07-31T11:28:06.293590"}, {"test": "<PERSON><PERSON> Test - receptionist", "status": "PASS", "message": "User +123456001d logged in successfully", "timestamp": "2025-07-31T11:28:06.340217"}, {"test": "Logout Test", "status": "PASS", "message": "User logged out successfully", "timestamp": "2025-07-31T11:28:06.355869"}, {"test": "Patient Registration", "status": "PASS", "message": "New patient registered successfully", "timestamp": "2025-07-31T11:28:08.845858"}, {"test": "Patient List View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:08.907044"}, {"test": "Patient Detail View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:08.944379"}, {"test": "Patient Wallet Operations", "status": "PASS", "message": "Wallet credit/debit working correctly", "timestamp": "2025-07-31T11:28:08.996974"}, {"test": "Doctor List View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.152134"}, {"test": "Doctor <PERSON><PERSON>", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.377052"}, {"test": "Doctor Schedule View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.480768"}, {"test": "Appointment Booking", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.589302"}, {"test": "Appointment List View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.630932"}, {"test": "Pharmacy Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.698510"}, {"test": "Medicine List View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.744465", "response_code": 200}, {"test": "Prescription Creation", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.791646"}, {"test": "Laboratory Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.877286"}, {"test": "Lab Tests View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.896827"}, {"test": "Billing Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:09.976097", "response_code": 200}, {"test": "Invoice List View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.044925", "response_code": 200}, {"test": "Invoice and Payment Creation", "status": "PASS", "message": "Invoice and payment created successfully", "timestamp": "2025-07-31T11:28:10.086938"}, {"test": "Inpatient Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.158223", "response_code": 200}, {"test": "Bed Management View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.212834", "response_code": 200}, {"test": "HR Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.290471", "response_code": 200}, {"test": "Employee List View", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.342801", "response_code": 200}, {"test": "Reporting Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.395084", "response_code": 200}, {"test": "NHIA Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.442892"}, {"test": "NHIA Patient Registration", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.469662"}, {"test": "Theatre Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.547252"}, {"test": "API Endpoint Access", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.556724"}, {"test": "Main Dashboard", "status": "PASS", "message": "", "timestamp": "2025-07-31T11:28:10.645497"}, {"test": "Core Functionality", "status": "PASS", "message": "Audit logging and notifications working", "timestamp": "2025-07-31T11:28:10.668144"}]}