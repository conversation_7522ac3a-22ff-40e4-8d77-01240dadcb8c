<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Hospital Management System{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />

    <!-- Custom CSS -->
    {% load static %}
{% load form_tags %}
    <link rel="stylesheet" href="{% static 'css/style_new.css' %}">
    <link rel="stylesheet" href="{% static 'css/sidebar.css' %}">
    <link rel="stylesheet" href="{% static 'css/dashboard_modern.css' %}">

    {% block extra_css %}{% endblock %}
    <style>
        /* Main layout for sidebar and content */
        html, body {
            height: 100%;
            overflow-x: hidden; /* Allow horizontal scrolling if needed */
            overflow-y: auto; /* Allow vertical scrolling */
        }
        #wrapper {
            display: flex;
            min-height: 100vh;
            width: 100vw;
            background: #f8f9fc;
            overflow-x: hidden; /* Keep horizontal hidden if desired, but allow vertical */
            overflow-y: auto; /* Allow vertical scrolling */
        }

        /* Content Wrapper */
        #content-wrapper {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            width: 100%;
            min-width: 0;
            background: #f8f9fc;
            overflow-y: auto; /* Changed from hidden to auto */
            height: 100vh;
        }

        /* Main Content Area */
        #content {
            flex: 1 0 auto;
            width: 100%;
            display: flex;
            flex-direction: column;
            padding-bottom: 80px; /* Space for footer */
            overflow-y: auto;
            height: 100vh;
        }

        /* Container Fluid for content padding */
        .container-fluid {
            flex: 1 0 auto;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            padding: 1rem;
        }

        /* Topbar custom style */
        .topbar {
            background: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.03);
            border-radius: 0 0 12px 12px;
            padding: 0.5rem 2rem;
            min-height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1020;
        }
        .topbar .navbar-brand {
            font-weight: 700;
            color: #00bfff !important;
            font-size: 1.4rem;
            letter-spacing: 1px;
        }
        .topbar .nav-link, .topbar .fa {
            color: #23272b !important;
            font-size: 1.1rem;
        }
        .topbar .nav-link:hover {
            color: #00bfff !important;
        }
        /* Footer custom style */
        footer, .footer {
            flex-shrink: 0;
            background: #fff;
            color: #23272b;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.03);
            border-radius: 12px 12px 0 0;
            padding: 1rem 2rem;
            text-align: center;
            font-size: 1rem;
            margin-top: auto;
            position: sticky;
            bottom: 0;
            width: 100%;
        }
        /* Responsive tweaks */
        @media (max-width: 991.98px) {
            .topbar, footer, .footer {
                padding: 0.5rem 1rem;
                border-radius: 0;
            }
        }
        @media (max-width: 600px) {
            .topbar, footer, .footer {
                padding: 0.5rem 0.5rem;
                font-size: 0.95rem;
            }
            .topbar .navbar-brand {
                font-size: 1.1rem;
            }
        }
    </style>
    <script>
        // Sidebar toggle for mobile - handled by sidebar.js
    </script>
    {% block extra_head %}
    {{ block.super }}
    <!-- Additional head content here -->
    {% endblock %}
</head>
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">
        <!-- Sidebar -->
        {% if user.is_authenticated %}
            {% include 'includes/sidebar.html' %}
        {% endif %}
        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <a class="navbar-brand" href="{% url 'dashboard:dashboard' %}">HMS</a>
                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ms-auto">
                        {% if user.is_authenticated %}
                            <li class="nav-item dropdown no-arrow">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                    data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <span class="me-2 d-none d-lg-inline text-gray-600 small">{{ user.get_full_name }}</span>
                                    {% if user.profile.profile_picture %}
                                        <img class="img-profile rounded-circle"
                                            src="{{ user.profile.profile_picture.url }}" alt="Profile Picture" style="width: 30px; height: 30px;">
                                    {% else %}
                                        <i class="fas fa-user-circle fa-2x text-gray-300"></i>
                                    {% endif %}
                                </a>
                                <!-- Dropdown - User Information -->
                                <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in"
                                    aria-labelledby="userDropdown">
                                    <a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                        <i class="fas fa-user fa-sm fa-fw me-2 text-gray-400"></i>
                                        Profile
                                    </a>
                                    <a class="dropdown-item" href="{% url 'accounts:edit_profile' %}">
                                        <i class="fas fa-cogs fa-sm fa-fw me-2 text-gray-400"></i>
                                        Settings
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#logoutModal">
                                        <i class="fas fa-sign-out-alt fa-sm fa-fw me-2 text-gray-400"></i>
                                        Logout
                                    </a>
                                </div>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'accounts:login' %}">
                                    <span class="me-2 d-none d-lg-inline text-gray-600 small">Login</span>
                                    <i class="fas fa-sign-in-alt"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                <!-- End of Topbar -->
                <!-- Begin Page Content -->
                <div class="container-fluid">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    {% block content %}
                        {% if 'bot' not in request.META.HTTP_USER_AGENT|default:''|lower %}
                            <div id="offline-banner"></div>
                        {% endif %}
                        <!-- BEGIN: Patient Widget -->
                        {% if patient %}
                        <div class="card mb-4" id="patient-widget">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Patient Quick Info</h5>
                                <span class="badge bg-primary">ID: {{ patient.patient_id }}</span>
                                <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#patientModal">
                                    <i class="fas fa-info-circle"></i> More
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Name:</strong> {{ patient.get_full_name }}<br>
                                        <strong>Gender:</strong> {{ patient.get_gender_display }}<br>
                                        <strong>Age:</strong> {{ age }}<br>
                                        <strong>Phone:</strong> {{ patient.phone_number }}<br>
                                        <strong>Email:</strong> {{ patient.email|default:'-' }}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>City:</strong> {{ patient.city|default:'-' }}<br>
                                        <strong>Blood Group:</strong> {{ patient.blood_group|default:'-' }}<br>
                                        <strong>Status:</strong> {% if patient.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}<br>
                                        <strong>Registered:</strong> {{ patient.registration_date|date:'M d, Y' }}
                                    </div>
                                </div>
                                {% if user.is_authenticated %}
                                <div class="row mt-3">
                                    <div class="col-12">
                                        {% if patient.is_active %}
                                            <form method="post" action="{% url 'patients:toggle_patient_status' patient.id %}" style="margin-top: 0.5rem;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-warning btn-sm">Deactivate Patient</button>
                                            </form>
                                        {% else %}
                                            <form method="post" action="{% url 'patients:toggle_patient_status' patient.id %}" style="margin-top: 0.5rem;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-success btn-sm">Activate Patient</button>
                                            </form>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <!-- Patient Modal -->
                        <div class="modal fade" id="patientModal" tabindex="-1" aria-labelledby="patientModalLabel" aria-hidden="true">
                          <div class="modal-dialog modal-lg modal-dialog-centered">
                            <div class="modal-content">
                              <div class="modal-header bg-info text-white">
                                <h5 class="modal-title" id="patientModalLabel">Patient Full Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                              </div>
                              <div class="modal-body">
                                <div class="row mb-2">
                                  <div class="col-md-6">
                                    <strong>Name:</strong> {{ patient.get_full_name }}<br>
                                    <strong>Patient ID:</strong> {{ patient.patient_id }}<br>
                                    <strong>Gender:</strong> {{ patient.get_gender_display }}<br>
                                    <strong>Age:</strong> {{ age }}<br>
                                    <strong>Phone:</strong> {{ patient.phone_number }}<br>
                                    <strong>Email:</strong> {{ patient.email|default:'-' }}<br>
                                    <strong>City:</strong> {{ patient.city|default:'-' }}<br>
                                    <strong>Address:</strong> {{ patient.address|default:'-' }}<br>
                                  </div>
                                  <div class="col-md-6">
                                    <strong>Blood Group:</strong> {{ patient.blood_group|default:'-' }}<br>
                                    <strong>Status:</strong> {% if patient.is_active %}<span class="badge bg-success">Active</span>{% else %}<span class="badge bg-danger">Inactive</span>{% endif %}<br>
                                    <strong>Registered:</strong> {{ patient.registration_date|date:'M d, Y' }}<br>
                                    <strong>Last Updated:</strong> {{ patient.updated_at|date:'M d, Y H:i' }}<br>
                                    <strong>Created By:</strong> {{ patient.created_by.get_full_name|default:patient.created_by.username }}<br>
                                    <strong>Updated By:</strong> {{ patient.updated_by.get_full_name|default:patient.updated_by.username }}<br>
                                  </div>
                                </div>
                                <div class="row">
                                  <div class="col-12">
                                    <strong>Notes:</strong> {{ patient.notes|default:'No notes'|linebreaks }}
                                  </div>
                                </div>
                              </div>
                              <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                              </div>
                            </div>
                          </div>
                        </div>
                        {% endif %}
                        <!-- END: Patient Widget -->
                        {% if medical_history_form %}
                        <!-- BEGIN: Add Medical History Widget -->
                        <div class="modal fade" id="addMedicalHistoryModal" tabindex="-1" aria-labelledby="addMedicalHistoryModalLabel" aria-hidden="true">
                          <div class="modal-dialog modal-lg modal-dialog-centered">
                            <div class="modal-content">
                              <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="addMedicalHistoryModalLabel">Add Medical History for {{ patient.get_full_name }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                              </div>
                              <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                <div class="modal-body">
                                  <div class="mb-3">
                                    {{ medical_history_form.non_field_errors }}
                                  </div>
                                  <div class="row g-3">
                                    <div class="col-md-6">
                                      <div class="form-group mb-2">
                                        <label for="id_condition">Condition</label>
                                        {{ medical_history_form.condition }}
                                      </div>
                                      <div class="form-group mb-2">
                                        <label for="id_date">Date</label>
                                        {{ medical_history_form.date }}
                                      </div>
                                      <div class="form-group mb-2">
                                        <label for="id_doctor_name">Doctor Name</label>
                                        {{ medical_history_form.doctor_name }}
                                      </div>
                                    </div>
                                    <div class="col-md-6">
                                      <div class="form-group mb-2">
                                        <label for="id_notes">Notes</label>
                                        {{ medical_history_form.notes }}
                                      </div>
                                      <div class="form-group mb-2">
                                        <label for="id_attachments">Attachments</label>
                                        {{ medical_history_form.attachments }}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="modal-footer">
                                  <button type="submit" name="add_medical_history" class="btn btn-primary">Add Medical History</button>
                                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                </div>
                              </form>
                            </div>
                          </div>
                        </div>
                        <!-- END: Add Medical History Widget -->
                        {% endif %}
                        <!-- Radiology Requests Widget -->
                {% if user.is_authenticated %}
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-info shadow-sm">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-x-ray me-2"></i>Recent Radiology Requests</h5>
                                <a href="{% url 'radiology:index' %}" class="btn btn-sm btn-light">View All</a>
                            </div>
                            <div class="card-body p-3">
                                {% if recent_radiology_orders %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Patient</th>
                                                    <th>Test</th>
                                                    <th>Status</th>
                                                    <th>Requested</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for order in recent_radiology_orders %}
                                                <tr>
                                                    <td>{{ order.patient.get_full_name }}</td>
                                                    <td>{{ order.test.name }}</td>
                                                    <td>{{ order.get_status_display }}</td>
                                                    <td>{{ order.order_date|date:'M d, Y H:i' }}</td>
                                                    <td><a href="{% url 'radiology:order_detail' order.id %}" class="btn btn-sm btn-outline-primary">Details</a></td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                {% else %}
                                    <div class="text-muted">No recent radiology requests.</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- End Radiology Requests Widget -->
                {% endif %}
                {% endblock %}
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->
            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>&copy; {{ year|default:2025 }} Hospital Management System. All rights reserved.</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                    <form method="post" action="{% url 'accounts:logout' %}" style="display:inline;">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-primary">Logout</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- jQuery Easing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-easing/1.4.1/jquery.easing.min.js"></script>

    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/sidebar.js' %}"></script>

    <!-- Modal Fix Script -->
    <script>
        $(document).ready(function() {
            // Ensure modals work properly with custom CSS
            $('.modal').on('show.bs.modal', function (e) {
                $('body').addClass('modal-open');
                $('#wrapper').addClass('modal-open');
                
                // Initialize Select2 for elements in the modal
                $(this).find('.select2').select2({
                    dropdownParent: $(this),
                    theme: 'bootstrap-5'
                });
            });
            
            $('.modal').on('shown.bs.modal', function (e) {
                // Focus on first input after modal is fully shown
                $(this).find('input, select, textarea').first().focus();
            });
            
            $('.modal').on('hidden.bs.modal', function (e) {
                $('body').removeClass('modal-open');
                $('#wrapper').removeClass('modal-open');
                
                // Destroy Select2 instances to prevent memory leaks
                $(this).find('.select2').select2('destroy');
            });
            
            // Fix for modal backdrop click issues
            $('.modal').on('click', function(e) {
                if (e.target === this) {
                    $(this).modal('hide');
                }
            });
            
            // Ensure modal content is clickable
            $('.modal-content').on('click', function(e) {
                e.stopPropagation();
            });
            
            // Prevent form submission on Enter key in modal inputs (except textareas)
            $('.modal').on('keypress', 'input:not([type="submit"])', function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="post">
                    {% csrf_token %}
                    <input type="hidden" name="add_item" value="1">

                    <div class="modal-header">
                        <h5 class="modal-title" id="addItemModalLabel">Add Invoice Item</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="{{ item_form.service.id_for_label }}" class="form-label">Service</label>
                            {{ item_form.service|add_class:"form-control select2-modal" }}
                            {% if item_form.service.errors %}
                                <div class="text-danger">
                                    {{ item_form.service.errors }}
                                </div>
                            {% endif %}
                            <div class="form-text">Select a service or enter a custom description below.</div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ item_form.description.id_for_label }}" class="form-label">Description</label>
                            {{ item_form.description|add_class:"form-control" }}
                            {% if item_form.description.errors %}
                                <div class="text-danger">
                                    {{ item_form.description.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ item_form.quantity.id_for_label }}" class="form-label">Quantity</label>
                                {{ item_form.quantity|add_class:"form-control" }}
                                {% if item_form.quantity.errors %}
                                    <div class="text-danger">
                                        {{ item_form.quantity.errors }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ item_form.unit_price.id_for_label }}" class="form-label">Unit Price</label>
                                {{ item_form.unit_price|add_class:"form-control" }}
                                {% if item_form.unit_price.errors %}
                                    <div class="text-danger">
                                        {{ item_form.unit_price.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Item</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
