/* Custom CSS for Hospital Management System */

/* General Styles */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #858796;
    text-align: left; /* Remove forced centering for all text */
    background-color: #f8f9fc;
    overflow-x: hidden;
    overflow-y: auto; /* Added to allow vertical scrolling */
}

.container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    max-width: 100vw;
    margin: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.container-fluid, .container-fluid.px-0 {
    padding-left: 0 !important;
    margin-left: 0 !important;
}

/* Navbar Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
    transition: color 0.3s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

.navbar-dark .navbar-nav .active > .nav-link {
    font-weight: 600;
}

/* Card Styles */
.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 20px;
    border: none;
    border-radius: 8px;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eaecf4;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard Icons */
.card i.fas {
    color: #007bff;
}

/* Form Styles */
.form-control {
    border-radius: 4px;
    border: 1px solid #d1d3e2;
    padding: 0.5rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Table Styles */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Button Styles */
.btn {
    border-radius: 4px;
    padding: 0.375rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.alert-primary {
    background-color: rgba(0, 123, 255, 0.15);
    color: #004085;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.15);
    color: #155724;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.15);
    color: #721c24;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.15);
    color: #856404;
}

/* Footer Styles */
.sticky-footer {
    padding: 2rem 0;
    flex-shrink: 0;
    background-color: #fff;
}

.sticky-footer .copyright {
    line-height: 1;
    font-size: 0.8rem;
    color: #858796;
}

.sticky-footer a {
    color: #4e73df;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sticky-footer a:hover {
    color: #224abe;
    text-decoration: underline;
}

/* Login/Register Forms */
.auth-form {
    max-width: 450px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    margin-left: auto !important;
    margin-right: auto !important;
    float: none !important;
    display: block;
}

/* Patient Profile */
.patient-profile-img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #f8f9fa;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Dashboard Stats */
.stats-card {
    border-left: 4px solid #007bff;
}

.stats-icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* Dropdown Menu Styles */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.5rem;
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
    transition: background-color 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

.dropdown-divider {
    border-top: 1px solid #e9ecef;
}

/* Pagination Styles */
.pagination .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #0056b3;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Badge Styles */
.badge {
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 15px;
    }

    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .sidebar {
        width: 0 !important;
    }

    .sidebar.toggled {
        width: 14rem !important;
    }

    .content {
        margin-left: 0;
    }

    .sidebar-toggled .content {
        margin-left: 0;
    }
}

/* Restore sidebar display and layout */
#wrapper {
    display: flex;
    flex-direction: row;
    width: 100%;
    min-height: 100vh;
    align-items: stretch;
    position: relative;
    gap: 0 !important;
}

.sidebar {
    display: block !important;
    width: 14rem !important;
    min-width: 12rem !important;
    padding: 0;
    margin: 0;
    background: #343a40 !important;
    color: #fff;
    min-height: 100vh;
    height: 100%;
    position: relative;
    z-index: 2;
    box-shadow: 2px 0 8px rgba(0,0,0,0.04);
}

#wrapper:before {
    content: '';
    display: block;
    background: #343a40;
    position: absolute;
    left: 0;
    top: 0;
    width: 14rem;
    height: 100%;
    min-height: 100vh;
    z-index: 1;
    pointer-events: none;
}

#content-wrapper {
    flex: 1 1 auto;
    width: auto;
    margin-left: 0;
    padding-left: 0;
    background: #f8f9fc;
    min-height: 100vh;
    position: relative;
    z-index: 2;
    /* Remove left margin that causes gap next to sidebar */
    margin-right: 0;
    /* Remove any extra left border or shadow */
    border-left: none !important;
    box-shadow: none !important;
    min-width: 0 !important;
    max-width: 100% !important;
}

/* Remove any extra padding/margin from the first child of #content-wrapper */
#content-wrapper > #content {
    margin-left: 0 !important;
    padding-left: 0 !important;
}

/* Remove any extra margin/padding from the first child of #content-wrapper */
#content-wrapper > *:first-child {
    margin-left: 0 !important;
    padding-left: 0 !important;
}

/* Remove sidebar width if any */
.sidebar {
    display: block !important;
    width: 14rem !important;
    min-width: 12rem !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Center auth forms and main content for login page */
.auth-form, .login-card, .card.auth-card {
    margin-left: auto !important;
    margin-right: auto !important;
    float: none !important;
    display: block;
}

body, html {
    height: 100%;
}

body.login-page, .login-page #wrapper, .login-page #content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

/* Remove any background or border from #content that could create a gap */
#content {
    margin-left: 0 !important;
    padding-left: 0 !important;
    background: transparent !important;
    border-left: none !important;
    box-shadow: none !important;
}

/* Remove margin or padding from body and html to ensure full width */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    width: 100vw;
    overflow-x: hidden;
}

/* Sidebar styles to ensure it sits flush left */
#sidebar {
    min-width: 250px;
    max-width: 250px;
    width: 250px;
    height: 100vh;
    position: relative;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
}

/* Content wrapper should take all remaining space */
#content-wrapper {
    flex: 1 1 0%;
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Remove container padding if any */
.container-fluid, .container {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Topbar and footer full width */
.topbar, footer, #content {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Prevent extra space from Bootstrap gutters */
.row, [class*="col-"] {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* === HMS Standalone Layout CSS (Context7 MCP Best Practice) === */

/* 1. Universal box-sizing and reset */
html, body {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    width: 100vw;
    min-height: 100vh;
    overflow-x: hidden;
    background: #f8f9fc;
    font-family: 'Segoe UI', Arial, sans-serif;
}
*, *::before, *::after {
    box-sizing: inherit;
}

/* 2. Layout Flexbox Structure */
#wrapper {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
    background: #f8f9fc;
}

/* 3. Sidebar Styles */
#sidebar, .sidebar {
    width: 250px;
    min-width: 250px;
    max-width: 250px;
    height: 100vh;
    background: #23272b;
    color: #fff;
    position: relative;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
    z-index: 100;
    display: flex;
    flex-direction: column;
}

/* 4. Content Area */
#content-wrapper {
    flex: 1 1 0%;
    width: 100%;
    min-width: 0;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    background: #f8f9fc;
    display: flex;
    flex-direction: column;
}
#content {
    flex: 1 1 0%;
    width: 100%;
    margin: 0;
    padding: 0;
    background: #f8f9fc;
    display: flex;
    flex-direction: column;
}

/* 5. Topbar and Footer Full Width */
.topbar, footer, #content > .topbar, #content > footer {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    left: 0;
    right: 0;
}

/* 6. Remove Bootstrap Gutters and Container Padding */
.container-fluid, .container {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
}
.row, [class*="col-"] {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* 7. Sidebar Navigation Links */
.sidebar a, #sidebar a {
    color: #fff;
    text-decoration: none;
    padding: 12px 24px;
    display: block;
    transition: background 0.2s;
}
.sidebar a.active, #sidebar a.active, .sidebar a:hover, #sidebar a:hover {
    background: #1a1d21;
    color: #00bfff;
}

/* 8. Responsive: Stack Sidebar on Small Screens */
@media (max-width: 991.98px) {
    #wrapper {
        flex-direction: column !important;
    }
    #sidebar, .sidebar {
        width: 100vw;
        min-width: 0;
        max-width: 100vw;
        height: auto;
        position: relative;
    }
    #content-wrapper {
        width: 100vw;
    }
}

/* 9. Utility: Hide Scrollbar for Sidebar */
#sidebar::-webkit-scrollbar, .sidebar::-webkit-scrollbar {
    width: 0;
    background: transparent;
}

/* 10. Miscellaneous */
.alert {
    margin: 16px 0;
}

/* 11. Accessibility: Focus Styles */
.sidebar a:focus, #sidebar a:focus {
    outline: 2px solid #00bfff;
    outline-offset: 2px;
}

/* === HMS Standalone Layout CSS: Improved Responsiveness & Styling === */

html, body {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    width: 100vw;
    min-height: 100vh;
    overflow-x: hidden;
    background: #f8f9fc;
    font-family: 'Segoe UI', Arial, sans-serif;
}
*, *::before, *::after {
    box-sizing: inherit;
}

/* Modal Fix - Ensure Bootstrap modals work properly */
.modal-backdrop {
    z-index: 1040 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    pointer-events: none !important;
}

.modal {
    z-index: 1050 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    outline: 0;
    pointer-events: auto !important;
}

.modal-dialog {
    z-index: 1060 !important;
    position: relative !important;
    pointer-events: auto !important;
    margin: 1.75rem auto !important;
    max-width: 500px !important;
}

/* Ensure modal content is clickable and properly positioned */
.modal-content {
    position: relative !important;
    z-index: 1070 !important;
    background-color: #fff !important;
    border: 1px solid rgba(0,0,0,.2) !important;
    border-radius: 0.3rem !important;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,.5) !important;
    pointer-events: auto !important;
    background-clip: padding-box !important;
    outline: 0 !important;
}

/* Fix for modal backdrop click-through issue */
body.modal-open {
    overflow: hidden !important;
    padding-right: 0 !important;
}

/* Ensure wrapper doesn't interfere with modal */
#wrapper.modal-open {
    overflow: hidden !important;
    pointer-events: none !important;
}

/* Re-enable pointer events for modal when wrapper is modal-open */
#wrapper.modal-open .modal {
    pointer-events: auto !important;
}

/* Prevent content wrapper from blocking modal interactions */
#content-wrapper {
    position: relative;
    z-index: 1;
}

/* Ensure sidebar doesn't interfere with modal */
#sidebar, .sidebar {
    z-index: 100;
}

/* Additional modal fixes */
.modal-backdrop.show {
    opacity: 0.5 !important;
}

/* Ensure modal is properly centered and accessible */
.modal.show .modal-dialog {
    transform: none !important;
}

/* Fix for form elements in modal */
.modal .form-control,
.modal .form-select,
.modal .btn {
    position: relative;
    z-index: 1;
    pointer-events: auto !important;
}

/* Ensure dropdown menus in modals work */
.modal .dropdown-menu {
    z-index: 1080 !important;
    pointer-events: auto !important;
}

/* Fix for date/time inputs in modals */
.modal input[type="datetime-local"],
.modal input[type="date"],
.modal input[type="time"] {
    z-index: 1;
    position: relative;
    pointer-events: auto !important;
}

/* Ensure all interactive elements in modal are clickable */
.modal button,
.modal input,
.modal select,
.modal textarea,
.modal a,
.modal label {
    pointer-events: auto !important;
}

/* Fix for modal header and footer */
.modal-header,
.modal-body,
.modal-footer {
    pointer-events: auto !important;
}

/* Ensure close button works */
.modal .btn-close {
    pointer-events: auto !important;
    z-index: 1080 !important;
}

#wrapper {
    display: flex;
    flex-direction: row;
    min-height: 100vh;
    width: 100vw;
    margin: 0;
    padding: 0;
    background: #f8f9fc;
    position: relative;
    z-index: 1;
}

/* Ensure wrapper doesn't block modal interactions */
#wrapper.modal-open {
    pointer-events: none !important;
}

/* But allow content within wrapper to be interactive when not modal */
#wrapper:not(.modal-open) {
    pointer-events: auto;
}

/* Sidebar Styles */
#sidebar, .sidebar {
    width: 250px;
    min-width: 220px;
    max-width: 300px;
    height: 100vh;
    background: linear-gradient(180deg, #23272b 80%, #1a1d21 100%);
    color: #fff;
    position: relative;
    left: 0;
    top: 0;
    margin: 0;
    padding: 0;
    z-index: 100;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 8px rgba(0,0,0,0.04);
    transition: width 0.2s;
}

.sidebar .sidebar-brand, #sidebar .sidebar-brand {
    padding: 24px 0 16px 0;
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: #00bfff;
    letter-spacing: 1px;
}

.sidebar .nav, #sidebar .nav {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;
    list-style: none;
}
.sidebar .nav a, #sidebar .nav a {
    color: #fff;
    text-decoration: none;
    padding: 12px 24px;
    display: block;
    border-left: 4px solid transparent;
    border-radius: 0 20px 20px 0;
    margin: 2px 0;
    transition: background 0.2s, color 0.2s, border-left 0.2s;
}
.sidebar .nav a.active, #sidebar .nav a.active, .sidebar .nav a:hover, #sidebar .nav a:hover {
    background: #1a1d21;
    color: #00bfff;
    border-left: 4px solid #00bfff;
}

.sidebar .sidebar-heading, #sidebar .sidebar-heading {
    padding: 16px 24px 8px 24px;
    font-size: 0.85rem;
    text-transform: uppercase;
    color: #b0b3b8;
    letter-spacing: 1px;
    font-weight: 600;
}

.sidebar hr, #sidebar hr {
    border: 0;
    border-top: 1px solid #444950;
    margin: 8px 0;
}

.sidebar .sidebar-footer, #sidebar .sidebar-footer {
    padding: 16px 24px;
    font-size: 0.9rem;
    color: #b0b3b8;
    border-top: 1px solid #444950;
}

/* Content Area */
#content-wrapper {
    flex: 1 1 0%;
    width: 100%;
    min-width: 0;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    background: #f8f9fc;
    display: flex;
    flex-direction: column;
}
#content {
    flex: 1 1 0%;
    width: 100%;
    margin: 0;
    padding: 0 1.5rem 1.5rem 1.5rem;
    background: #f8f9fc;
    display: flex;
    flex-direction: column;
}

.topbar, footer, #content > .topbar, #content > footer {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    border-radius: 0 0 8px 8px;
}

.container-fluid, .container {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100% !important;
}
.row, [class*="col-"] {
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.alert {
    margin: 16px 0;
}

.sidebar a:focus, #sidebar a:focus {
    outline: 2px solid #00bfff;
    outline-offset: 2px;
}

/* Responsive: Sidebar collapses, content full width */
@media (max-width: 991.98px) {
    #wrapper {
        flex-direction: column !important;
    }
    #sidebar, .sidebar {
        width: 100vw;
        min-width: 0;
        max-width: 100vw;
        height: auto;
        position: relative;
        flex-direction: row;
        box-shadow: none;
        border-radius: 0;
    }
    #content-wrapper {
        width: 100vw;
    }
    #content {
        padding: 1rem 0.5rem 1.5rem 0.5rem;
    }
}

/* Mobile: Sidebar collapses to icon bar */
@media (max-width: 600px) {
    #sidebar, .sidebar {
        width: 60px;
        min-width: 60px;
        max-width: 60px;
        padding: 0;
        align-items: center;
    }
    .sidebar .sidebar-brand, #sidebar .sidebar-brand,
    .sidebar .sidebar-heading, #sidebar .sidebar-heading,
    .sidebar .sidebar-footer, #sidebar .sidebar-footer {
        display: none;
    }
    .sidebar .nav a, #sidebar .nav a {
        padding: 12px 8px;
        text-align: center;
        font-size: 1.2rem;
        border-radius: 8px;
        margin: 2px 0;
    }
}

/* Smooth transitions for sidebar and content */
#sidebar, .sidebar, #content, #content-wrapper {
    transition: all 0.2s cubic-bezier(.4,0,.2,1);
}
